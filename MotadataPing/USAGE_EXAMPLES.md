# Motadata Ping - Usage Examples

This document provides comprehensive examples of how to use the Motadata Ping utility.

## Basic Usage Examples

### 1. Ping a Single Host
```bash
MotadataPing.exe google.com
```

### 2. Ping Multiple Hosts
```bash
MotadataPing.exe google.com microsoft.com github.com
```

### 3. Ping with Custom Settings
```bash
MotadataPing.exe google.com --count 10 --timeout 3000 --ttl 128 --size 64
```

## File Input Examples

### 4. Ping Hosts from File
```bash
MotadataPing.exe --file hosts.txt
```

### 5. Combine Command Line and File Input
```bash
MotadataPing.exe google.com --file additional-hosts.txt
```

## Output Format Examples

### 6. Save Results to CSV
```bash
MotadataPing.exe --file hosts.txt --format Csv --output-file results.csv
```

### 7. Save Results to JSON
```bash
MotadataPing.exe google.com microsoft.com --format Json --output-file results.json
```

## Performance and Concurrency Examples

### 8. High Concurrency for Large Host Lists
```bash
MotadataPing.exe --file large-hosts.txt --concurrency 50 --count 1
```

### 9. Conservative Settings for Slow Networks
```bash
MotadataPing.exe --file hosts.txt --concurrency 5 --timeout 10000 --retry 3
```

## Monitoring and Automation Examples

### 10. Quick Connectivity Check (No Statistics)
```bash
MotadataPing.exe ******* ******* --count 1 --no-statistics --no-progress
```

### 11. Detailed Network Analysis
```bash
MotadataPing.exe --file critical-servers.txt --count 20 --retry 2 --retry-delay 500 --format Json --output-file network-analysis.json
```

### 12. Silent Operation for Scripts
```bash
MotadataPing.exe --file hosts.txt --no-progress --no-statistics --format Csv --output-file status.csv
```

## Advanced Configuration Examples

### 13. Custom Packet Size and TTL
```bash
MotadataPing.exe --file hosts.txt --size 1024 --ttl 255 --timeout 8000
```

### 14. Rapid Ping with Minimal Delay
```bash
MotadataPing.exe google.com --count 100 --timeout 1000 --concurrency 1
```

### 15. Network Troubleshooting
```bash
MotadataPing.exe problematic-host.com --count 50 --retry 5 --retry-delay 2000 --verbose
```

## Sample Host Files

### hosts.txt (Basic Format)
```
google.com
microsoft.com
github.com
*******
*******
```

### network-devices.txt (Mixed Format)
```
# Core network infrastructure
router.local
switch.local
firewall.local

# Public DNS servers
*******
*******
*******
*******

# External services
google.com
microsoft.com
```

### servers.csv (CSV Format)
```
web-server-1.company.com,db-server-1.company.com
mail-server.company.com
backup-server.company.com,monitoring.company.com
```

## Real-World Scenarios

### 16. Data Center Health Check
```bash
MotadataPing.exe --file datacenter-hosts.txt --count 5 --concurrency 20 --format Json --output-file datacenter-health.json
```

### 17. Internet Connectivity Test
```bash
MotadataPing.exe ******* ******* ************** --count 3 --timeout 2000
```

### 18. Load Balancer Testing
```bash
MotadataPing.exe lb1.company.com lb2.company.com lb3.company.com --count 100 --concurrency 3
```

### 19. Network Latency Baseline
```bash
MotadataPing.exe --file regional-servers.txt --count 50 --format Csv --output-file latency-baseline.csv
```

### 20. Automated Monitoring Script
```bash
# Windows batch script example
@echo off
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
MotadataPing.exe --file monitoring-hosts.txt --format Json --output-file "monitoring_%TIMESTAMP%.json" --no-progress
if %ERRORLEVEL% EQU 0 (
    echo Monitoring completed successfully
) else (
    echo Monitoring failed with error %ERRORLEVEL%
)
```

## Performance Optimization Tips

### 21. Optimize for Speed
```bash
# For maximum speed with many hosts
MotadataPing.exe --file hosts.txt --count 1 --timeout 2000 --concurrency 50 --no-progress
```

### 22. Optimize for Accuracy
```bash
# For maximum accuracy and reliability
MotadataPing.exe --file hosts.txt --count 10 --timeout 10000 --retry 3 --concurrency 5
```

### 23. Optimize for Minimal Network Impact
```bash
# For minimal network load
MotadataPing.exe --file hosts.txt --count 1 --concurrency 1 --retry-delay 5000
```

## Error Handling Examples

### 24. Handle DNS Resolution Issues
```bash
# Mix of IPs and hostnames to avoid DNS dependency
MotadataPing.exe ******* google.com ******* cloudflare.com --retry 2
```

### 25. Timeout Handling for Slow Networks
```bash
MotadataPing.exe --file remote-hosts.txt --timeout 15000 --retry 3 --retry-delay 3000
```

## Integration Examples

### 26. PowerShell Integration
```powershell
# PowerShell script to process results
$results = & MotadataPing.exe --file hosts.txt --format Json --output-file temp.json --no-progress
$data = Get-Content temp.json | ConvertFrom-Json
$failedHosts = $data.results | Where-Object { $_.status -ne 0 }
Write-Host "Failed hosts: $($failedHosts.Count)"
```

### 27. Scheduled Task Example
```bash
# Run every 15 minutes
MotadataPing.exe --file critical-hosts.txt --count 1 --format Csv --output-file "C:\Monitoring\ping_$(Get-Date -Format 'yyyyMMdd_HHmm').csv" --no-progress
```

These examples demonstrate the flexibility and power of the Motadata Ping utility for various network monitoring and troubleshooting scenarios.
