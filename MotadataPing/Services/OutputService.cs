using System.Globalization;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using MotadataPing.Models;

namespace MotadataPing.Services;

/// <summary>
/// Service for formatting and outputting ping results in various formats
/// </summary>
public class OutputService : IOutputService
{
    private readonly ILogger<OutputService> _logger;
    private readonly object _consoleLock = new();

    public OutputService(ILogger<OutputService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public void WriteToConsole(PingResult result)
    {
        lock (_consoleLock)
        {
            var timestamp = result.Timestamp.ToString("HH:mm:ss", CultureInfo.InvariantCulture);
            
            if (result.IsSuccess)
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"[{timestamp}] PING {result.Host} ({result.IpAddress}): " +
                                $"time={result.RoundTripTimeMs}ms TTL={result.Ttl}");
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"[{timestamp}] PING {result.Host} ({result.IpAddress ?? "unresolved"}): " +
                                $"{result.StatusDescription}");
                
                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    Console.WriteLine($"    Error: {result.ErrorMessage}");
                }
            }
            
            Console.ResetColor();
        }
    }

    public void WriteProgress(PingProgress progress)
    {
        lock (_consoleLock)
        {
            var progressBar = CreateProgressBar(progress.ProgressPercentage);
            Console.Write($"\r{progressBar} {progress.ProgressPercentage:F1}% " +
                         $"({progress.CompletedAttempts}/{progress.TotalAttempts}) " +
                         $"Current: {progress.CurrentHost}");
        }
    }

    public void WriteStatistics(PingStatistics statistics)
    {
        lock (_consoleLock)
        {
            Console.WriteLine();
            Console.WriteLine("=== PING STATISTICS ===");
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine(statistics.GetSummary());
            Console.ResetColor();
            Console.WriteLine();
        }
    }

    public async Task SaveToFileAsync(IEnumerable<PingResult> results, string filePath, OutputFormat format, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Saving results to {FilePath} in {Format} format", filePath, format);

            var content = format switch
            {
                OutputFormat.Csv => FormatAsCsv(results),
                OutputFormat.Json => FormatAsJson(results),
                _ => throw new ArgumentException($"Unsupported output format: {format}")
            };

            await File.WriteAllTextAsync(filePath, content, Encoding.UTF8, cancellationToken);
            WriteInfo($"Results saved to: {filePath}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving results to file: {FilePath}", filePath);
            WriteError($"Failed to save results to file: {ex.Message}");
            throw;
        }
    }

    public string FormatAsCsv(IEnumerable<PingResult> results)
    {
        var csv = new StringBuilder();
        
        // Header
        csv.AppendLine("Host,IpAddress,Status,RoundTripTimeMs,Ttl,PacketSize,Timestamp,ErrorMessage,AttemptNumber");
        
        // Data rows
        foreach (var result in results)
        {
            csv.AppendLine($"{EscapeCsvField(result.Host)}," +
                          $"{EscapeCsvField(result.IpAddress)}," +
                          $"{result.Status}," +
                          $"{result.RoundTripTimeMs?.ToString(CultureInfo.InvariantCulture) ?? ""}," +
                          $"{result.Ttl?.ToString(CultureInfo.InvariantCulture) ?? ""}," +
                          $"{result.PacketSize}," +
                          $"{result.Timestamp:yyyy-MM-dd HH:mm:ss.fff}," +
                          $"{EscapeCsvField(result.ErrorMessage)}," +
                          $"{result.AttemptNumber}");
        }
        
        return csv.ToString();
    }

    public string FormatAsJson(IEnumerable<PingResult> results)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        var data = new
        {
            GeneratedAt = DateTime.UtcNow,
            Results = results.ToList()
        };

        return JsonSerializer.Serialize(data, options);
    }

    public void WriteError(string message)
    {
        lock (_consoleLock)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"ERROR: {message}");
            Console.ResetColor();
        }
    }

    public void WriteInfo(string message)
    {
        lock (_consoleLock)
        {
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine($"INFO: {message}");
            Console.ResetColor();
        }
    }

    public void WriteWarning(string message)
    {
        lock (_consoleLock)
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"WARNING: {message}");
            Console.ResetColor();
        }
    }

    private static string EscapeCsvField(string? field)
    {
        if (string.IsNullOrEmpty(field))
            return "";

        if (field.Contains(',') || field.Contains('"') || field.Contains('\n') || field.Contains('\r'))
        {
            return $"\"{field.Replace("\"", "\"\"")}\"";
        }

        return field;
    }

    private static string CreateProgressBar(double percentage)
    {
        const int barLength = 20;
        var filledLength = (int)(barLength * percentage / 100);
        var bar = new string('█', filledLength) + new string('░', barLength - filledLength);
        return $"[{bar}]";
    }
}
