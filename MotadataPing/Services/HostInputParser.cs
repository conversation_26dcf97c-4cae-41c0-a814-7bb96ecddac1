using System.Net;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace MotadataPing.Services;

/// <summary>
/// Service for parsing and validating host inputs from various sources
/// </summary>
public partial class HostInputParser : IHostInputParser
{
    private readonly ILogger<HostInputParser> _logger;

    // Regex patterns for validation
    [GeneratedRegex(@"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$")]
    private static partial Regex HostnameRegex();

    [GeneratedRegex(@"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")]
    private static partial Regex IPv4Regex();

    [GeneratedRegex(@"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$")]
    private static partial Regex IPv6Regex();

    public HostInputParser(ILogger<HostInputParser> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<IEnumerable<string>> ParseFromFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

        if (!File.Exists(filePath))
            throw new FileNotFoundException($"File not found: {filePath}");

        try
        {
            _logger.LogInformation("Parsing hosts from file: {FilePath}", filePath);

            var lines = await File.ReadAllLinesAsync(filePath, cancellationToken);
            var hosts = new List<string>();

            foreach (var line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.TrimStart().StartsWith('#'))
                    continue; // Skip empty lines and comments

                // Handle both CSV and space-separated formats
                var lineHosts = ParseFromString(line);
                hosts.AddRange(lineHosts);
            }

            var uniqueHosts = hosts.Distinct(StringComparer.OrdinalIgnoreCase).ToList();
            _logger.LogInformation("Parsed {Count} unique hosts from file", uniqueHosts.Count);

            return uniqueHosts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing hosts from file: {FilePath}", filePath);
            throw;
        }
    }

    public IEnumerable<string> ParseFromString(string hostString)
    {
        if (string.IsNullOrWhiteSpace(hostString))
            return Enumerable.Empty<string>();

        // Split by comma, semicolon, space, or tab
        var separators = new[] { ',', ';', ' ', '\t', '\n', '\r' };
        var hosts = hostString
            .Split(separators, StringSplitOptions.RemoveEmptyEntries)
            .Select(SanitizeHost)
            .Where(host => !string.IsNullOrWhiteSpace(host) && IsValidHost(host))
            .Distinct(StringComparer.OrdinalIgnoreCase)
            .ToList();

        _logger.LogDebug("Parsed {Count} hosts from string input", hosts.Count);
        return hosts;
    }

    public bool IsValidHost(string host)
    {
        if (string.IsNullOrWhiteSpace(host))
            return false;

        host = host.Trim();

        // Check if it's a valid IPv4 address
        if (IPv4Regex().IsMatch(host))
        {
            return IPAddress.TryParse(host, out var ipv4) && ipv4.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
        }

        // Check if it's a valid IPv6 address
        if (IPv6Regex().IsMatch(host) || host.Contains(':'))
        {
            return IPAddress.TryParse(host, out var ipv6) && ipv6.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6;
        }

        // Check if it's a valid hostname/FQDN
        if (host.Length > 253) // Maximum length for FQDN
            return false;

        // Basic hostname validation
        return HostnameRegex().IsMatch(host);
    }

    public string SanitizeHost(string host)
    {
        if (string.IsNullOrWhiteSpace(host))
            return string.Empty;

        // Remove common prefixes and suffixes
        host = host.Trim()
            .TrimStart("http://".ToCharArray())
            .TrimStart("https://".ToCharArray())
            .TrimStart("ftp://".ToCharArray());

        // Remove port numbers
        var colonIndex = host.LastIndexOf(':');
        if (colonIndex > 0 && !host.Contains("::")) // Not IPv6
        {
            var portPart = host.Substring(colonIndex + 1);
            if (int.TryParse(portPart, out _))
            {
                host = host.Substring(0, colonIndex);
            }
        }

        // Remove trailing slashes and paths
        var slashIndex = host.IndexOf('/');
        if (slashIndex > 0)
        {
            host = host.Substring(0, slashIndex);
        }

        // Convert to lowercase for consistency
        return host.ToLowerInvariant();
    }
}
