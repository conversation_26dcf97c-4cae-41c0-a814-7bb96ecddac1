using MotadataPing.Models;

namespace MotadataPing.Services;

/// <summary>
/// Interface for ping operations
/// </summary>
public interface IPingService
{
    /// <summary>
    /// Performs a single ping to the specified host
    /// </summary>
    /// <param name="host">Target host to ping</param>
    /// <param name="configuration">Ping configuration</param>
    /// <param name="attemptNumber">Attempt number for retry scenarios</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Ping result</returns>
    Task<PingResult> PingAsync(string host, PingConfiguration configuration, int attemptNumber = 1, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs bulk ping operations on multiple hosts
    /// </summary>
    /// <param name="configuration">Ping configuration containing hosts and settings</param>
    /// <param name="progress">Progress reporter for bulk operations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of ping results</returns>
    Task<IEnumerable<PingResult>> PingBulkAsync(PingConfiguration configuration, IProgress<PingProgress>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs multiple ping attempts to a single host
    /// </summary>
    /// <param name="host">Target host to ping</param>
    /// <param name="configuration">Ping configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of ping results for all attempts</returns>
    Task<IEnumerable<PingResult>> PingMultipleAsync(string host, PingConfiguration configuration, CancellationToken cancellationToken = default);
}

/// <summary>
/// Progress information for bulk ping operations
/// </summary>
public record PingProgress
{
    /// <summary>
    /// Current host being pinged
    /// </summary>
    public required string CurrentHost { get; init; }

    /// <summary>
    /// Number of hosts completed
    /// </summary>
    public int CompletedHosts { get; init; }

    /// <summary>
    /// Total number of hosts to ping
    /// </summary>
    public int TotalHosts { get; init; }

    /// <summary>
    /// Number of ping attempts completed
    /// </summary>
    public int CompletedAttempts { get; init; }

    /// <summary>
    /// Total number of ping attempts
    /// </summary>
    public int TotalAttempts { get; init; }

    /// <summary>
    /// Progress percentage (0-100)
    /// </summary>
    public double ProgressPercentage => TotalAttempts > 0 ? (double)CompletedAttempts / TotalAttempts * 100 : 0;

    /// <summary>
    /// Latest ping result
    /// </summary>
    public PingResult? LatestResult { get; init; }
}
