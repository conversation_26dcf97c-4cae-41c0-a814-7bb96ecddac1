using MotadataPing.Models;

namespace MotadataPing.Services;

/// <summary>
/// Interface for output formatting and writing services
/// </summary>
public interface IOutputService
{
    /// <summary>
    /// Writes ping results to console in real-time
    /// </summary>
    /// <param name="result">Ping result to display</param>
    void WriteToConsole(PingResult result);

    /// <summary>
    /// Writes progress information to console
    /// </summary>
    /// <param name="progress">Progress information</param>
    void WriteProgress(PingProgress progress);

    /// <summary>
    /// Writes statistics summary to console
    /// </summary>
    /// <param name="statistics">Statistics to display</param>
    void WriteStatistics(PingStatistics statistics);

    /// <summary>
    /// Saves ping results to a file in the specified format
    /// </summary>
    /// <param name="results">Collection of ping results</param>
    /// <param name="filePath">Output file path</param>
    /// <param name="format">Output format</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SaveToFileAsync(IEnumerable<PingResult> results, string filePath, OutputFormat format, CancellationToken cancellationToken = default);

    /// <summary>
    /// Formats ping results as CSV string
    /// </summary>
    /// <param name="results">Collection of ping results</param>
    /// <returns>CSV formatted string</returns>
    string FormatAsCsv(IEnumerable<PingResult> results);

    /// <summary>
    /// Formats ping results as JSON string
    /// </summary>
    /// <param name="results">Collection of ping results</param>
    /// <returns>JSON formatted string</returns>
    string FormatAsJson(IEnumerable<PingResult> results);

    /// <summary>
    /// Writes an error message to console
    /// </summary>
    /// <param name="message">Error message</param>
    void WriteError(string message);

    /// <summary>
    /// Writes an informational message to console
    /// </summary>
    /// <param name="message">Information message</param>
    void WriteInfo(string message);

    /// <summary>
    /// Writes a warning message to console
    /// </summary>
    /// <param name="message">Warning message</param>
    void WriteWarning(string message);
}
