namespace MotadataPing.Services;

/// <summary>
/// Interface for parsing host inputs from various sources
/// </summary>
public interface IHostInputParser
{
    /// <summary>
    /// Parses hosts from a file (CSV or TXT format)
    /// </summary>
    /// <param name="filePath">Path to the file containing hosts</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of parsed hosts</returns>
    Task<IEnumerable<string>> ParseFromFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Parses hosts from a comma or space-separated string
    /// </summary>
    /// <param name="hostString">String containing hosts separated by commas or spaces</param>
    /// <returns>Collection of parsed hosts</returns>
    IEnumerable<string> ParseFromString(string hostString);

    /// <summary>
    /// Validates that a host string is in a valid format
    /// </summary>
    /// <param name="host">Host to validate</param>
    /// <returns>True if the host is valid, false otherwise</returns>
    bool IsValidHost(string host);

    /// <summary>
    /// Sanitizes and normalizes a host string
    /// </summary>
    /// <param name="host">Host to sanitize</param>
    /// <returns>Sanitized host string</returns>
    string SanitizeHost(string host);
}
