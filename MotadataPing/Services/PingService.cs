using System.Net;
using System.Net.NetworkInformation;
using Microsoft.Extensions.Logging;
using MotadataPing.Models;

namespace MotadataPing.Services;

/// <summary>
/// Production-ready ping service implementation with comprehensive error handling
/// </summary>
public class PingService : IPingService
{
    private readonly ILogger<PingService> _logger;
    private readonly SemaphoreSlim _concurrencySemaphore;

    public PingService(ILogger<PingService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _concurrencySemaphore = new SemaphoreSlim(Environment.ProcessorCount * 2);
    }

    public async Task<PingResult> PingAsync(string host, PingConfiguration configuration, int attemptNumber = 1, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(host))
            throw new ArgumentException("Host cannot be null or empty", nameof(host));

        using var ping = new Ping();
        var buffer = new byte[configuration.PacketSize];
        var options = new PingOptions(configuration.Ttl, true);

        try
        {
            _logger.LogDebug("Pinging {Host} (attempt {AttemptNumber})", host, attemptNumber);

            // Resolve IP address first for better error reporting
            string? resolvedIp = null;
            try
            {
                var addresses = await Dns.GetHostAddressesAsync(host, cancellationToken);
                resolvedIp = addresses.FirstOrDefault()?.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to resolve host {Host}: {Error}", host, ex.Message);
                return PingResult.Failure(host, null, IPStatus.BadDestination, configuration.PacketSize, 
                    $"DNS resolution failed: {ex.Message}", attemptNumber);
            }

            var reply = await ping.SendPingAsync(host, configuration.TimeoutMs, buffer, options);

            if (reply.Status == IPStatus.Success)
            {
                _logger.LogDebug("Ping to {Host} successful: {RoundTripTime}ms", host, reply.RoundtripTime);
                return PingResult.Success(host, resolvedIp, reply.RoundtripTime, reply.Options?.Ttl, 
                    configuration.PacketSize, attemptNumber);
            }
            else
            {
                _logger.LogDebug("Ping to {Host} failed: {Status}", host, reply.Status);
                return PingResult.Failure(host, resolvedIp, reply.Status, configuration.PacketSize, 
                    null, attemptNumber);
            }
        }
        catch (PingException ex)
        {
            _logger.LogWarning("Ping exception for {Host}: {Error}", host, ex.Message);
            return PingResult.Failure(host, null, IPStatus.IcmpError, configuration.PacketSize, 
                ex.Message, attemptNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error pinging {Host}", host);
            return PingResult.Failure(host, null, IPStatus.IcmpError, configuration.PacketSize, 
                ex.Message, attemptNumber);
        }
    }

    public async Task<IEnumerable<PingResult>> PingMultipleAsync(string host, PingConfiguration configuration, CancellationToken cancellationToken = default)
    {
        var results = new List<PingResult>();
        
        for (int i = 1; i <= configuration.PingCount; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var result = await PingAsync(host, configuration, i, cancellationToken);
            results.Add(result);

            // Add delay between pings (except for the last one)
            if (i < configuration.PingCount)
            {
                await Task.Delay(1000, cancellationToken);
            }
        }

        return results;
    }

    public async Task<IEnumerable<PingResult>> PingBulkAsync(PingConfiguration configuration, IProgress<PingProgress>? progress = null, CancellationToken cancellationToken = default)
    {
        var allResults = new List<PingResult>();
        var totalAttempts = configuration.Hosts.Count * configuration.PingCount;
        var completedAttempts = 0;
        var completedHosts = 0;

        // Create semaphore for concurrency control
        using var semaphore = new SemaphoreSlim(configuration.MaxConcurrency);

        var tasks = configuration.Hosts.Select(async host =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var hostResults = new List<PingResult>();
                
                for (int attempt = 1; attempt <= configuration.PingCount; attempt++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var result = await PingAsync(host, configuration, attempt, cancellationToken);
                    hostResults.Add(result);

                    // Handle retries for failed pings
                    if (!result.IsSuccess && configuration.RetryAttempts > 0)
                    {
                        for (int retry = 1; retry <= configuration.RetryAttempts; retry++)
                        {
                            await Task.Delay(configuration.RetryDelayMs, cancellationToken);
                            var retryResult = await PingAsync(host, configuration, attempt, cancellationToken);
                            
                            if (retryResult.IsSuccess)
                            {
                                hostResults[hostResults.Count - 1] = retryResult; // Replace failed result with successful retry
                                break;
                            }
                        }
                    }

                    var currentCompleted = Interlocked.Increment(ref completedAttempts);
                    
                    progress?.Report(new PingProgress
                    {
                        CurrentHost = host,
                        CompletedHosts = completedHosts,
                        TotalHosts = configuration.Hosts.Count,
                        CompletedAttempts = currentCompleted,
                        TotalAttempts = totalAttempts,
                        LatestResult = result
                    });

                    // Add delay between pings to the same host
                    if (attempt < configuration.PingCount)
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                }

                Interlocked.Increment(ref completedHosts);
                return hostResults;
            }
            finally
            {
                semaphore.Release();
            }
        });

        var results = await Task.WhenAll(tasks);
        
        foreach (var hostResults in results)
        {
            allResults.AddRange(hostResults);
        }

        return allResults;
    }

    public void Dispose()
    {
        _concurrencySemaphore?.Dispose();
    }
}
