using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MotadataPing.Models;
using MotadataPing.Services;

namespace MotadataPing;

/// <summary>
/// Main program entry point for the Motadata Ping utility
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        try
        {
            // Parse command line arguments manually for now
            var (config, inputFile) = ParseArguments(args);
            if (config == null)
            {
                ShowHelp();
                return 1;
            }

            await ExecutePingOperationAsync(config, inputFile);
            return 0;
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"Fatal error: {ex.Message}");
            Console.ResetColor();
            return 1;
        }
    }

    private static (PingConfiguration?, string?) ParseArguments(string[] args)
    {
        var hosts = new List<string>();
        string? inputFile = null;
        int ttl = 64;
        int packetSize = 32;
        int timeout = 5000;
        int count = 4;
        int concurrency = 10;
        var format = OutputFormat.Console;
        string? outputFile = null;
        int retry = 1;
        int retryDelay = 1000;
        bool showProgress = true;
        bool includeStatistics = true;
        bool verbose = false;

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--help" or "-h":
                    return (null, null);
                case "--file" or "-f":
                    if (i + 1 < args.Length) inputFile = args[++i];
                    break;
                case "--ttl" or "-t":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out ttl);
                    break;
                case "--size" or "-s":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out packetSize);
                    break;
                case "--timeout" or "-w":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out timeout);
                    break;
                case "--count" or "-c":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out count);
                    break;
                case "--concurrency" or "-j":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out concurrency);
                    break;
                case "--format" or "-o":
                    if (i + 1 < args.Length) Enum.TryParse(args[++i], true, out format);
                    break;
                case "--output-file" or "-O":
                    if (i + 1 < args.Length) outputFile = args[++i];
                    break;
                case "--retry" or "-r":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out retry);
                    break;
                case "--retry-delay" or "-d":
                    if (i + 1 < args.Length) int.TryParse(args[++i], out retryDelay);
                    break;
                case "--no-progress" or "-q":
                    showProgress = false;
                    break;
                case "--no-statistics" or "-n":
                    includeStatistics = false;
                    break;
                case "--verbose" or "-v":
                    verbose = true;
                    break;
                default:
                    if (!args[i].StartsWith('-'))
                    {
                        hosts.Add(args[i]);
                    }
                    break;
            }
        }

        var config = new PingConfiguration
        {
            Hosts = hosts.AsReadOnly(),
            Ttl = ttl,
            PacketSize = packetSize,
            TimeoutMs = timeout,
            PingCount = count,
            MaxConcurrency = concurrency,
            OutputFormat = format,
            OutputFilePath = outputFile,
            RetryAttempts = retry,
            RetryDelayMs = retryDelay,
            ShowProgress = showProgress,
            IncludeStatistics = includeStatistics
        };

        return (config, inputFile);
    }

    private static void ShowHelp()
    {
        Console.WriteLine("Motadata Ping - Production-ready bulk ping utility for Windows OS");
        Console.WriteLine();
        Console.WriteLine("Usage:");
        Console.WriteLine("  MotadataPing [hosts...] [options]");
        Console.WriteLine();
        Console.WriteLine("Arguments:");
        Console.WriteLine("  hosts                     Hosts to ping (space separated)");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -f, --file <path>         Path to file containing hosts");
        Console.WriteLine("  -t, --ttl <value>         Time To Live (default: 64)");
        Console.WriteLine("  -s, --size <bytes>        Packet size in bytes (default: 32)");
        Console.WriteLine("  -w, --timeout <ms>        Timeout in milliseconds (default: 5000)");
        Console.WriteLine("  -c, --count <number>      Number of pings per host (default: 4)");
        Console.WriteLine("  -j, --concurrency <num>   Max concurrent operations (default: 10)");
        Console.WriteLine("  -o, --format <format>     Output format: Console, Csv, Json (default: Console)");
        Console.WriteLine("  -O, --output-file <path>  Output file path");
        Console.WriteLine("  -r, --retry <number>      Retry attempts for failed pings (default: 1)");
        Console.WriteLine("  -d, --retry-delay <ms>    Delay between retries (default: 1000)");
        Console.WriteLine("  -q, --no-progress         Disable progress reporting");
        Console.WriteLine("  -n, --no-statistics       Disable statistics");
        Console.WriteLine("  -v, --verbose             Enable verbose logging");
        Console.WriteLine("  -h, --help                Show this help");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  MotadataPing google.com microsoft.com");
        Console.WriteLine("  MotadataPing --file hosts.txt --count 10");
        Console.WriteLine("  MotadataPing --file hosts.txt --format Csv --output-file results.csv");
    }

    private static async Task ExecutePingOperationAsync(PingConfiguration configuration, string? inputFile)
    {
        // Setup dependency injection and logging
        var services = new ServiceCollection();
        ConfigureServices(services);
        
        using var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var outputService = serviceProvider.GetRequiredService<IOutputService>();
        var hostParser = serviceProvider.GetRequiredService<IHostInputParser>();
        var pingService = serviceProvider.GetRequiredService<IPingService>();

        try
        {
            // Parse and validate hosts
            var allHosts = new List<string>();
            
            // Add hosts from command line
            if (configuration.Hosts.Any())
            {
                var parsedHosts = hostParser.ParseFromString(string.Join(" ", configuration.Hosts));
                allHosts.AddRange(parsedHosts);
            }

            // Add hosts from file
            if (!string.IsNullOrEmpty(inputFile) && File.Exists(inputFile))
            {
                var fileHosts = await hostParser.ParseFromFileAsync(inputFile);
                allHosts.AddRange(fileHosts);
            }

            if (!allHosts.Any())
            {
                outputService.WriteError("No valid hosts specified. Use --help for usage information.");
                return;
            }

            // Update configuration with parsed hosts
            var updatedConfig = configuration with { Hosts = allHosts.Distinct(StringComparer.OrdinalIgnoreCase).ToList().AsReadOnly() };

            // Validate configuration
            var validationErrors = updatedConfig.Validate().ToList();
            if (validationErrors.Any())
            {
                outputService.WriteError("Configuration validation failed:");
                foreach (var error in validationErrors)
                {
                    outputService.WriteError($"  - {error}");
                }
                return;
            }

            // Validate output file requirement for non-console formats
            if (updatedConfig.OutputFormat != OutputFormat.Console && string.IsNullOrEmpty(updatedConfig.OutputFilePath))
            {
                outputService.WriteError($"Output file path is required for {updatedConfig.OutputFormat} format. Use --output-file option.");
                return;
            }

            // Display operation summary
            outputService.WriteInfo($"Starting ping operation for {updatedConfig.Hosts.Count} hosts");
            outputService.WriteInfo($"Configuration: TTL={updatedConfig.Ttl}, PacketSize={updatedConfig.PacketSize}B, Timeout={updatedConfig.TimeoutMs}ms, Count={updatedConfig.PingCount}, Concurrency={updatedConfig.MaxConcurrency}");
            
            if (updatedConfig.RetryAttempts > 0)
            {
                outputService.WriteInfo($"Retry configuration: {updatedConfig.RetryAttempts} attempts with {updatedConfig.RetryDelayMs}ms delay");
            }

            Console.WriteLine();

            // Execute ping operations
            var stopwatch = Stopwatch.StartNew();
            var results = new List<PingResult>();

            IProgress<PingProgress>? progress = null;
            if (updatedConfig.ShowProgress)
            {
                progress = new Progress<PingProgress>(p =>
                {
                    outputService.WriteProgress(p);
                    if (p.LatestResult != null && updatedConfig.OutputFormat == OutputFormat.Console)
                    {
                        Console.WriteLine(); // New line after progress
                        outputService.WriteToConsole(p.LatestResult);
                    }
                });
            }

            var pingResults = await pingService.PingBulkAsync(updatedConfig, progress);
            results.AddRange(pingResults);

            stopwatch.Stop();

            // Clear progress line
            if (updatedConfig.ShowProgress)
            {
                Console.WriteLine();
            }

            // Display statistics
            if (updatedConfig.IncludeStatistics)
            {
                var statistics = PingStatistics.FromResults(results, stopwatch.Elapsed);
                outputService.WriteStatistics(statistics);
            }

            // Save to file if specified
            if (!string.IsNullOrEmpty(updatedConfig.OutputFilePath))
            {
                await outputService.SaveToFileAsync(results, updatedConfig.OutputFilePath, updatedConfig.OutputFormat);
            }

            outputService.WriteInfo($"Operation completed in {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during ping operation");
            outputService.WriteError($"Operation failed: {ex.Message}");
        }
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // Register services
        services.AddSingleton<IPingService, PingService>();
        services.AddSingleton<IHostInputParser, HostInputParser>();
        services.AddSingleton<IOutputService, OutputService>();
    }
}
