using System.Diagnostics;
using System.Globalization;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace MotadataPing;

public partial class Program
{
    private const int DefaultTtl = 64;
    private const int DefaultPacketSize = 56;
    private const int DefaultTimeout = 500;
    private const int DefaultCount = 3;
    private const int DefaultConcurrency = 25;
    private const int DefaultRetry = 3;
    private const int DefaultRetryDelay = 500;

    [GeneratedRegex(@"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$")]
    private static partial Regex HostnameRegex();

    [GeneratedRegex(@"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")]
    private static partial Regex IPv4Regex();

    public static async Task<int> Main(string[] args)
    {
        try
        {
            var config = ParseArguments(args);
            if (config == null) { ShowHelp(); return 1; }
            await ExecutePingAsync(config);
            return 0;
        }
        catch (Exception ex) { WriteError($"Fatal error: {ex.Message}"); return 1; }
    }

    private static PingConfig? ParseArguments(string[] args)
    {
        var hosts = new List<string>();
        string? inputFile = null;
        int ttl = DefaultTtl, packetSize = DefaultPacketSize, timeout = DefaultTimeout;
        int count = DefaultCount, concurrency = DefaultConcurrency;
        string format = "Console";
        string? outputFile = null;
        int retry = DefaultRetry, retryDelay = DefaultRetryDelay;
        bool showProgress = true, includeStatistics = true;

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--help" or "-h": return null;
                case "--file" or "-f": if (i + 1 < args.Length) inputFile = args[++i]; break;
                case "--ttl" or "-t": if (i + 1 < args.Length) int.TryParse(args[++i], out ttl); break;
                case "--size" or "-s" or "-b": if (i + 1 < args.Length) int.TryParse(args[++i], out packetSize); break;
                case "--timeout" or "-w": if (i + 1 < args.Length) int.TryParse(args[++i], out timeout); break;
                case "--count" or "-c": if (i + 1 < args.Length) int.TryParse(args[++i], out count); break;
                case "--concurrency" or "-j": if (i + 1 < args.Length) int.TryParse(args[++i], out concurrency); break;
                case "--format" or "-o": if (i + 1 < args.Length) format = args[++i]; break;
                case "--output-file" or "-O": if (i + 1 < args.Length) outputFile = args[++i]; break;
                case "--retry" or "-r": if (i + 1 < args.Length) int.TryParse(args[++i], out retry); break;
                case "--retry-delay" or "-d": if (i + 1 < args.Length) int.TryParse(args[++i], out retryDelay); break;
                case "--no-progress" or "-q": showProgress = false; break;
                case "--no-statistics" or "-n": includeStatistics = false; break;
                default: if (!args[i].StartsWith('-')) hosts.Add(args[i]); break;
            }
        }

        return new PingConfig
        {
            Hosts = hosts, InputFile = inputFile, Ttl = ttl, PacketSize = packetSize,
            Timeout = timeout, Count = count, Concurrency = concurrency, Format = format,
            OutputFile = outputFile, Retry = retry, RetryDelay = retryDelay,
            ShowProgress = showProgress, IncludeStatistics = includeStatistics
        };
    }

    private static void ShowHelp()
    {
        Console.WriteLine("Motadata Ping - Simplified bulk ping utility with fping-compatible defaults");
        Console.WriteLine("\nUsage: MotadataPing [hosts...] [options]");
        Console.WriteLine("\nOptions:");
        Console.WriteLine("  -f, --file <path>         Path to file containing hosts");
        Console.WriteLine("  -t, --ttl <value>         Time To Live (default: 64)");
        Console.WriteLine("  -s, --size <bytes>        Packet size in bytes (default: 56, fping compatible)");
        Console.WriteLine("  -b <bytes>                Alias for --size (fping compatibility)");
        Console.WriteLine("  -w, --timeout <ms>        Timeout in milliseconds (default: 500, fping compatible)");
        Console.WriteLine("  -c, --count <number>      Number of pings per host (default: 3, fping compatible)");
        Console.WriteLine("  -j, --concurrency <num>   Max concurrent operations (default: 25)");
        Console.WriteLine("  -o, --format <format>     Output format: Console, Csv, Json (default: Console)");
        Console.WriteLine("  -O, --output-file <path>  Output file path");
        Console.WriteLine("  -r, --retry <number>      Retry attempts for failed pings (default: 3, fping compatible)");
        Console.WriteLine("  -d, --retry-delay <ms>    Delay between retries (default: 500, fping compatible)");
        Console.WriteLine("  -q, --no-progress         Disable progress reporting");
        Console.WriteLine("  -n, --no-statistics       Disable statistics");
        Console.WriteLine("  -h, --help                Show this help");
        Console.WriteLine("\nExamples:");
        Console.WriteLine("  MotadataPing google.com microsoft.com");
        Console.WriteLine("  MotadataPing --file hosts.txt --count 5");
        Console.WriteLine("  MotadataPing --file hosts.txt --format Csv --output-file results.csv");
    }

    private static async Task ExecutePingAsync(PingConfig config)
    {
        var allHosts = new List<string>();
        allHosts.AddRange(ParseHosts(string.Join(" ", config.Hosts)));

        if (!string.IsNullOrEmpty(config.InputFile) && File.Exists(config.InputFile))
        {
            var fileHosts = await ParseHostsFromFileAsync(config.InputFile);
            allHosts.AddRange(fileHosts);
        }

        if (!allHosts.Any()) { WriteError("No valid hosts specified. Use --help for usage information."); return; }

        var uniqueHosts = allHosts.Distinct(StringComparer.OrdinalIgnoreCase).ToList();

        if (config.Format.ToLower() != "console" && string.IsNullOrEmpty(config.OutputFile))
        {
            WriteError($"Output file path is required for {config.Format} format. Use --output-file option.");
            return;
        }

        WriteInfo($"Starting ping operation for {uniqueHosts.Count} hosts");
        WriteInfo($"fping-compatible settings: PacketSize={config.PacketSize}B, Timeout={config.Timeout}ms, Count={config.Count}, Concurrency={config.Concurrency}");
        Console.WriteLine();

        var stopwatch = Stopwatch.StartNew();
        var results = await PingHostsAsync(uniqueHosts, config);
        stopwatch.Stop();

        Console.WriteLine();
        if (config.IncludeStatistics) DisplayStatistics(results, stopwatch.Elapsed);
        if (!string.IsNullOrEmpty(config.OutputFile)) await SaveResultsAsync(results, config.OutputFile, config.Format);
        WriteInfo($"Operation completed in {stopwatch.Elapsed.TotalSeconds:F2} seconds");
    }

    private static async Task<List<PingResult>> PingHostsAsync(List<string> hosts, PingConfig config)
    {
        var results = new List<PingResult>();
        var totalAttempts = hosts.Count * config.Count;
        var completedAttempts = 0;

        using var semaphore = new SemaphoreSlim(config.Concurrency);

        var tasks = hosts.Select(async host =>
        {
            await semaphore.WaitAsync();
            try
            {
                var hostResults = new List<PingResult>();

                for (int attempt = 1; attempt <= config.Count; attempt++)
                {
                    var result = await PingHostAsync(host, config, attempt);
                    hostResults.Add(result);

                    if (!result.IsSuccess && config.Retry > 0)
                    {
                        for (int retry = 1; retry <= config.Retry; retry++)
                        {
                            await Task.Delay(config.RetryDelay);
                            var retryResult = await PingHostAsync(host, config, attempt);
                            if (retryResult.IsSuccess) { hostResults[hostResults.Count - 1] = retryResult; break; }
                        }
                    }

                    var completed = Interlocked.Increment(ref completedAttempts);

                    if (config.ShowProgress)
                    {
                        var progress = (double)completed / totalAttempts * 100;
                        var progressBar = CreateProgressBar(progress);
                        Console.Write($"\r{progressBar} {progress:F1}% ({completed}/{totalAttempts}) Current: {host}");
                    }

                    if (config.Format.ToLower() == "console")
                    {
                        if (config.ShowProgress) Console.WriteLine();
                        DisplayResult(result);
                    }

                    if (attempt < config.Count) await Task.Delay(1000);
                }

                return hostResults;
            }
            finally { semaphore.Release(); }
        });

        var allResults = await Task.WhenAll(tasks);
        foreach (var hostResults in allResults) results.AddRange(hostResults);
        return results;
    }

    private static async Task<PingResult> PingHostAsync(string host, PingConfig config, int attemptNumber)
    {
        using var ping = new Ping();
        var buffer = new byte[config.PacketSize];
        var options = new PingOptions(config.Ttl, true);

        try
        {
            string? resolvedIp = null;
            try
            {
                var addresses = await Dns.GetHostAddressesAsync(host);
                resolvedIp = addresses.FirstOrDefault()?.ToString();
            }
            catch (Exception ex)
            {
                return new PingResult
                {
                    Host = host, IpAddress = null, Status = IPStatus.BadDestination,
                    PacketSize = config.PacketSize, ErrorMessage = $"DNS resolution failed: {ex.Message}",
                    AttemptNumber = attemptNumber, Timestamp = DateTime.UtcNow
                };
            }

            var reply = await ping.SendPingAsync(host, config.Timeout, buffer, options);

            return new PingResult
            {
                Host = host, IpAddress = resolvedIp, Status = reply.Status,
                RoundTripTimeMs = reply.Status == IPStatus.Success ? reply.RoundtripTime : null,
                Ttl = reply.Options?.Ttl, PacketSize = config.PacketSize,
                AttemptNumber = attemptNumber, Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new PingResult
            {
                Host = host, IpAddress = null, Status = IPStatus.IcmpError,
                PacketSize = config.PacketSize, ErrorMessage = ex.Message,
                AttemptNumber = attemptNumber, Timestamp = DateTime.UtcNow
            };
        }
    }

    private static List<string> ParseHosts(string hostString)
    {
        if (string.IsNullOrWhiteSpace(hostString)) return new List<string>();
        var separators = new[] { ',', ';', ' ', '\t', '\n', '\r' };
        return hostString.Split(separators, StringSplitOptions.RemoveEmptyEntries)
            .Select(SanitizeHost).Where(host => !string.IsNullOrWhiteSpace(host) && IsValidHost(host))
            .Distinct(StringComparer.OrdinalIgnoreCase).ToList();
    }

    private static async Task<List<string>> ParseHostsFromFileAsync(string filePath)
    {
        var lines = await File.ReadAllLinesAsync(filePath);
        var hosts = new List<string>();
        foreach (var line in lines)
        {
            if (string.IsNullOrWhiteSpace(line) || line.TrimStart().StartsWith('#')) continue;
            hosts.AddRange(ParseHosts(line));
        }
        return hosts.Distinct(StringComparer.OrdinalIgnoreCase).ToList();
    }

    private static bool IsValidHost(string host)
    {
        if (string.IsNullOrWhiteSpace(host)) return false;
        host = host.Trim();
        if (IPv4Regex().IsMatch(host)) return IPAddress.TryParse(host, out var ipv4) && ipv4.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
        if (host.Contains(':')) return IPAddress.TryParse(host, out var ipv6) && ipv6.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6;
        return host.Length <= 253 && HostnameRegex().IsMatch(host);
    }

    private static string SanitizeHost(string host)
    {
        if (string.IsNullOrWhiteSpace(host)) return string.Empty;
        host = host.Trim().Replace("http://", "").Replace("https://", "").Replace("ftp://", "");
        var colonIndex = host.LastIndexOf(':');
        if (colonIndex > 0 && !host.Contains("::"))
        {
            var portPart = host.Substring(colonIndex + 1);
            if (int.TryParse(portPart, out _)) host = host.Substring(0, colonIndex);
        }
        var slashIndex = host.IndexOf('/');
        if (slashIndex > 0) host = host.Substring(0, slashIndex);
        return host.ToLowerInvariant();
    }

    private static void DisplayResult(PingResult result)
    {
        var timestamp = result.Timestamp.ToString("HH:mm:ss", CultureInfo.InvariantCulture);
        if (result.IsSuccess)
        {
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"[{timestamp}] PING {result.Host} ({result.IpAddress}): time={result.RoundTripTimeMs}ms TTL={result.Ttl}");
        }
        else
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"[{timestamp}] PING {result.Host} ({result.IpAddress ?? "unresolved"}): {GetStatusDescription(result.Status)}");
            if (!string.IsNullOrEmpty(result.ErrorMessage)) Console.WriteLine($"    Error: {result.ErrorMessage}");
        }
        Console.ResetColor();
    }

    private static string GetStatusDescription(IPStatus status) => status switch
    {
        IPStatus.Success => "Success", IPStatus.TimedOut => "Timed out",
        IPStatus.DestinationHostUnreachable => "Host unreachable", IPStatus.DestinationNetworkUnreachable => "Network unreachable",
        IPStatus.DestinationUnreachable => "Destination unreachable", IPStatus.BadDestination => "Bad destination",
        IPStatus.BadHeader => "Bad header", IPStatus.BadOption => "Bad option", IPStatus.BadRoute => "Bad route",
        IPStatus.HardwareError => "Hardware error", IPStatus.IcmpError => "ICMP error", IPStatus.NoResources => "No resources",
        IPStatus.PacketTooBig => "Packet too big", IPStatus.ParameterProblem => "Parameter problem",
        IPStatus.SourceQuench => "Source quench", IPStatus.TtlExpired => "TTL expired",
        IPStatus.TtlReassemblyTimeExceeded => "TTL reassembly time exceeded", _ => status.ToString()
    };

    private static void DisplayStatistics(List<PingResult> results, TimeSpan executionTime)
    {
        var successfulResults = results.Where(r => r.IsSuccess).ToList();
        var hostGroups = results.GroupBy(r => r.Host).ToList();
        var roundTripTimes = successfulResults.Where(r => r.RoundTripTimeMs.HasValue).Select(r => r.RoundTripTimeMs!.Value).ToList();

        Console.WriteLine("\n=== PING STATISTICS ===");
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.WriteLine($"Total Hosts: {hostGroups.Count}");
        Console.WriteLine($"Total Attempts: {results.Count}");
        Console.WriteLine($"Successful: {successfulResults.Count} ({(double)successfulResults.Count / results.Count * 100:F1}%)");
        Console.WriteLine($"Failed: {results.Count - successfulResults.Count} ({(double)(results.Count - successfulResults.Count) / results.Count * 100:F1}%)");
        Console.WriteLine($"Reachable Hosts: {hostGroups.Count(g => g.Any(r => r.IsSuccess))}");
        Console.WriteLine($"Unreachable Hosts: {hostGroups.Count(g => g.All(r => !r.IsSuccess))}");
        Console.WriteLine($"Execution Time: {executionTime.TotalSeconds:F2}s");
        if (roundTripTimes.Any()) Console.WriteLine($"Round-trip time: min={roundTripTimes.Min()}ms, max={roundTripTimes.Max()}ms, avg={roundTripTimes.Average():F1}ms");
        Console.ResetColor();
        Console.WriteLine();
    }

    private static async Task SaveResultsAsync(List<PingResult> results, string filePath, string format)
    {
        try
        {
            string content = format.ToLower() switch
            {
                "csv" => FormatAsCsv(results),
                "json" => FormatAsJson(results),
                _ => throw new ArgumentException($"Unsupported format: {format}")
            };
            await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);
            WriteInfo($"Results saved to: {filePath}");
        }
        catch (Exception ex) { WriteError($"Failed to save results: {ex.Message}"); }
    }

    private static string FormatAsCsv(List<PingResult> results)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Host,IpAddress,Status,RoundTripTimeMs,Ttl,PacketSize,Timestamp,ErrorMessage,AttemptNumber");
        foreach (var result in results)
        {
            csv.AppendLine($"{EscapeCsvField(result.Host)},{EscapeCsvField(result.IpAddress)},{result.Status}," +
                          $"{result.RoundTripTimeMs?.ToString(CultureInfo.InvariantCulture) ?? ""},{result.Ttl?.ToString(CultureInfo.InvariantCulture) ?? ""}," +
                          $"{result.PacketSize},{result.Timestamp:yyyy-MM-dd HH:mm:ss.fff},{EscapeCsvField(result.ErrorMessage)},{result.AttemptNumber}");
        }
        return csv.ToString();
    }

    private static string FormatAsJson(List<PingResult> results)
    {
        var data = new
        {
            GeneratedAt = DateTime.UtcNow,
            Results = results.Select(r => new
            {
                r.Host, r.IpAddress, Status = r.Status.ToString(), r.RoundTripTimeMs, r.Ttl,
                r.PacketSize, r.Timestamp, r.ErrorMessage, r.AttemptNumber, IsSuccess = r.IsSuccess
            })
        };
        return JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
    }

    private static string EscapeCsvField(string? field)
    {
        if (string.IsNullOrEmpty(field)) return "";
        if (field.Contains(',') || field.Contains('"') || field.Contains('\n') || field.Contains('\r'))
            return $"\"{field.Replace("\"", "\"\"")}\"";
        return field;
    }

    private static string CreateProgressBar(double percentage)
    {
        const int barLength = 20;
        var filledLength = (int)(barLength * percentage / 100);
        var bar = new string('█', filledLength) + new string('░', barLength - filledLength);
        return $"[{bar}]";
    }

    private static void WriteError(string message)
    {
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine($"ERROR: {message}");
        Console.ResetColor();
    }

    private static void WriteInfo(string message)
    {
        Console.ForegroundColor = ConsoleColor.White;
        Console.WriteLine($"INFO: {message}");
        Console.ResetColor();
    }

    private record PingConfig
    {
        public List<string> Hosts { get; init; } = new();
        public string? InputFile { get; init; }
        public int Ttl { get; init; }
        public int PacketSize { get; init; }
        public int Timeout { get; init; }
        public int Count { get; init; }
        public int Concurrency { get; init; }
        public string Format { get; init; } = "Console";
        public string? OutputFile { get; init; }
        public int Retry { get; init; }
        public int RetryDelay { get; init; }
        public bool ShowProgress { get; init; }
        public bool IncludeStatistics { get; init; }
    }

    private record PingResult
    {
        public required string Host { get; init; }
        public string? IpAddress { get; init; }
        public IPStatus Status { get; init; }
        public long? RoundTripTimeMs { get; init; }
        public int? Ttl { get; init; }
        public int PacketSize { get; init; }
        public DateTime Timestamp { get; init; }
        public string? ErrorMessage { get; init; }
        public int AttemptNumber { get; init; }
        public bool IsSuccess => Status == IPStatus.Success;
    }
}
