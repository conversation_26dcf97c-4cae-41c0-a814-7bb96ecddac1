using System.CommandLine;
using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MotadataPing.Models;
using MotadataPing.Services;
using MotadataPing.Utilities;

namespace MotadataPing;

/// <summary>
/// Main program entry point for the Motadata Ping utility
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        try
        {
            var rootCommand = CommandLineParser.CreateRootCommand();
            
            rootCommand.SetHandler(async (
                string[] hosts,
                string? file,
                int ttl,
                int packetSize,
                int timeout,
                int count,
                int concurrency,
                OutputFormat format,
                string? outputFile,
                int retry,
                int retryDelay,
                bool noProgress,
                bool noStatistics,
                bool verbose) =>
            {
                await ExecutePingOperationAsync(hosts, file, ttl, packetSize, timeout, count, 
                    concurrency, format, outputFile, retry, retryDelay, noProgress, noStatistics, verbose);
            },
            rootCommand.Arguments.Cast<Argument<string[]>>().First(),
            rootCommand.Options.Cast<Option<string?>>().ElementAt(0),
            rootCommand.Options.Cast<Option<int>>().ElementAt(0),
            rootCommand.Options.Cast<Option<int>>().ElementAt(1),
            rootCommand.Options.Cast<Option<int>>().ElementAt(2),
            rootCommand.Options.Cast<Option<int>>().ElementAt(3),
            rootCommand.Options.Cast<Option<int>>().ElementAt(4),
            rootCommand.Options.Cast<Option<OutputFormat>>().First(),
            rootCommand.Options.Cast<Option<string?>>().ElementAt(1),
            rootCommand.Options.Cast<Option<int>>().ElementAt(5),
            rootCommand.Options.Cast<Option<int>>().ElementAt(6),
            rootCommand.Options.Cast<Option<bool>>().ElementAt(0),
            rootCommand.Options.Cast<Option<bool>>().ElementAt(1),
            rootCommand.Options.Cast<Option<bool>>().ElementAt(2));

            return await rootCommand.InvokeAsync(args);
        }
        catch (Exception ex)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"Fatal error: {ex.Message}");
            Console.ResetColor();
            return 1;
        }
    }

    private static async Task ExecutePingOperationAsync(
        string[] hosts,
        string? file,
        int ttl,
        int packetSize,
        int timeout,
        int count,
        int concurrency,
        OutputFormat format,
        string? outputFile,
        int retry,
        int retryDelay,
        bool noProgress,
        bool noStatistics,
        bool verbose)
    {
        // Setup dependency injection and logging
        var services = new ServiceCollection();
        ConfigureServices(services, verbose);
        
        using var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var outputService = serviceProvider.GetRequiredService<IOutputService>();
        var hostParser = serviceProvider.GetRequiredService<IHostInputParser>();
        var pingService = serviceProvider.GetRequiredService<IPingService>();

        try
        {
            // Parse and validate hosts
            var allHosts = new List<string>();
            
            // Add hosts from command line
            if (hosts.Length > 0)
            {
                var parsedHosts = hostParser.ParseFromString(string.Join(" ", hosts));
                allHosts.AddRange(parsedHosts);
            }

            // Add hosts from file
            if (!string.IsNullOrEmpty(file))
            {
                var fileHosts = await hostParser.ParseFromFileAsync(file);
                allHosts.AddRange(fileHosts);
            }

            if (!allHosts.Any())
            {
                outputService.WriteError("No valid hosts specified. Use --help for usage information.");
                return;
            }

            // Create configuration
            var configuration = new PingConfiguration
            {
                Hosts = allHosts.Distinct(StringComparer.OrdinalIgnoreCase).ToList().AsReadOnly(),
                Ttl = ttl,
                PacketSize = packetSize,
                TimeoutMs = timeout,
                PingCount = count,
                MaxConcurrency = concurrency,
                OutputFormat = format,
                OutputFilePath = outputFile,
                RetryAttempts = retry,
                RetryDelayMs = retryDelay,
                ShowProgress = !noProgress,
                IncludeStatistics = !noStatistics
            };

            // Validate configuration
            var validationErrors = configuration.Validate().ToList();
            if (validationErrors.Any())
            {
                outputService.WriteError("Configuration validation failed:");
                foreach (var error in validationErrors)
                {
                    outputService.WriteError($"  - {error}");
                }
                return;
            }

            // Validate output file requirement for non-console formats
            if (format != OutputFormat.Console && string.IsNullOrEmpty(outputFile))
            {
                outputService.WriteError($"Output file path is required for {format} format. Use --output-file option.");
                return;
            }

            // Display operation summary
            outputService.WriteInfo($"Starting ping operation for {configuration.Hosts.Count} hosts");
            outputService.WriteInfo($"Configuration: TTL={ttl}, PacketSize={packetSize}B, Timeout={timeout}ms, Count={count}, Concurrency={concurrency}");
            
            if (configuration.RetryAttempts > 0)
            {
                outputService.WriteInfo($"Retry configuration: {retry} attempts with {retryDelay}ms delay");
            }

            Console.WriteLine();

            // Execute ping operations
            var stopwatch = Stopwatch.StartNew();
            var results = new List<PingResult>();

            IProgress<PingProgress>? progress = null;
            if (configuration.ShowProgress)
            {
                progress = new Progress<PingProgress>(p =>
                {
                    outputService.WriteProgress(p);
                    if (p.LatestResult != null && format == OutputFormat.Console)
                    {
                        Console.WriteLine(); // New line after progress
                        outputService.WriteToConsole(p.LatestResult);
                    }
                });
            }

            var pingResults = await pingService.PingBulkAsync(configuration, progress);
            results.AddRange(pingResults);

            stopwatch.Stop();

            // Clear progress line
            if (configuration.ShowProgress)
            {
                Console.WriteLine();
            }

            // Display statistics
            if (configuration.IncludeStatistics)
            {
                var statistics = PingStatistics.FromResults(results, stopwatch.Elapsed);
                outputService.WriteStatistics(statistics);
            }

            // Save to file if specified
            if (!string.IsNullOrEmpty(outputFile))
            {
                await outputService.SaveToFileAsync(results, outputFile, format);
            }

            outputService.WriteInfo($"Operation completed in {stopwatch.Elapsed.TotalSeconds:F2} seconds");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during ping operation");
            outputService.WriteError($"Operation failed: {ex.Message}");
            
            if (verbose)
            {
                outputService.WriteError($"Stack trace: {ex.StackTrace}");
            }
        }
    }

    private static void ConfigureServices(IServiceCollection services, bool verbose)
    {
        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(verbose ? LogLevel.Debug : LogLevel.Information);
        });

        // Register services
        services.AddSingleton<IPingService, PingService>();
        services.AddSingleton<IHostInputParser, HostInputParser>();
        services.AddSingleton<IOutputService, OutputService>();
    }
}
