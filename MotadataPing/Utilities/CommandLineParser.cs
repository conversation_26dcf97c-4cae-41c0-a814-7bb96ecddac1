using System.CommandLine;
using MotadataPing.Models;

namespace MotadataPing.Utilities;

/// <summary>
/// Command-line argument parser using System.CommandLine
/// </summary>
public static class CommandLineParser
{
    /// <summary>
    /// Creates the root command with all options and arguments
    /// </summary>
    public static RootCommand CreateRootCommand()
    {
        var rootCommand = new RootCommand("Motadata Ping - Production-ready bulk ping utility for Windows OS")
        {
            CreateHostsArgument(),
            CreateFileOption(),
            CreateTtlOption(),
            CreatePacketSizeOption(),
            CreateTimeoutOption(),
            CreateCountOption(),
            CreateConcurrencyOption(),
            CreateOutputFormatOption(),
            CreateOutputFileOption(),
            CreateRetryOption(),
            CreateRetryDelayOption(),
            CreateNoProgressOption(),
            CreateNoStatisticsOption(),
            CreateVerboseOption()
        };

        return rootCommand;
    }

    private static Argument<string[]> CreateHostsArgument()
    {
        return new Argument<string[]>(
            name: "hosts",
            description: "Hosts to ping (space or comma separated). Can be hostnames, IPv4, or IPv6 addresses.")
        {
            Arity = ArgumentArity.ZeroOrMore
        };
    }

    private static Option<string?> CreateFileOption()
    {
        return new Option<string?>(
            aliases: new[] { "--file", "-f" },
            description: "Path to file containing hosts (CSV or TXT format, one host per line or comma-separated)")
        {
            ArgumentHelpName = "path"
        };
    }

    private static Option<int> CreateTtlOption()
    {
        return new Option<int>(
            aliases: new[] { "--ttl", "-t" },
            description: "Time To Live (TTL) for ping packets",
            getDefaultValue: () => 64)
        {
            ArgumentHelpName = "value"
        };
    }

    private static Option<int> CreatePacketSizeOption()
    {
        return new Option<int>(
            aliases: new[] { "--size", "-s" },
            description: "Size of ping packets in bytes",
            getDefaultValue: () => 32)
        {
            ArgumentHelpName = "bytes"
        };
    }

    private static Option<int> CreateTimeoutOption()
    {
        return new Option<int>(
            aliases: new[] { "--timeout", "-w" },
            description: "Timeout for each ping attempt in milliseconds",
            getDefaultValue: () => 5000)
        {
            ArgumentHelpName = "ms"
        };
    }

    private static Option<int> CreateCountOption()
    {
        return new Option<int>(
            aliases: new[] { "--count", "-c" },
            description: "Number of ping attempts per host",
            getDefaultValue: () => 4)
        {
            ArgumentHelpName = "number"
        };
    }

    private static Option<int> CreateConcurrencyOption()
    {
        return new Option<int>(
            aliases: new[] { "--concurrency", "-j" },
            description: "Maximum number of concurrent ping operations",
            getDefaultValue: () => 10)
        {
            ArgumentHelpName = "number"
        };
    }

    private static Option<OutputFormat> CreateOutputFormatOption()
    {
        return new Option<OutputFormat>(
            aliases: new[] { "--format", "-o" },
            description: "Output format (Console, Csv, Json)",
            getDefaultValue: () => OutputFormat.Console)
        {
            ArgumentHelpName = "format"
        };
    }

    private static Option<string?> CreateOutputFileOption()
    {
        return new Option<string?>(
            aliases: new[] { "--output-file", "-O" },
            description: "Path to save output file (required for CSV and JSON formats)")
        {
            ArgumentHelpName = "path"
        };
    }

    private static Option<int> CreateRetryOption()
    {
        return new Option<int>(
            aliases: new[] { "--retry", "-r" },
            description: "Number of retry attempts for failed pings",
            getDefaultValue: () => 1)
        {
            ArgumentHelpName = "number"
        };
    }

    private static Option<int> CreateRetryDelayOption()
    {
        return new Option<int>(
            aliases: new[] { "--retry-delay", "-d" },
            description: "Delay between retry attempts in milliseconds",
            getDefaultValue: () => 1000)
        {
            ArgumentHelpName = "ms"
        };
    }

    private static Option<bool> CreateNoProgressOption()
    {
        return new Option<bool>(
            aliases: new[] { "--no-progress", "-q" },
            description: "Disable progress reporting during bulk operations",
            getDefaultValue: () => false);
    }

    private static Option<bool> CreateNoStatisticsOption()
    {
        return new Option<bool>(
            aliases: new[] { "--no-statistics", "-n" },
            description: "Disable statistics reporting at completion",
            getDefaultValue: () => false);
    }

    private static Option<bool> CreateVerboseOption()
    {
        return new Option<bool>(
            aliases: new[] { "--verbose", "-v" },
            description: "Enable verbose logging",
            getDefaultValue: () => false);
    }

    /// <summary>
    /// Creates a PingConfiguration from parsed command line arguments
    /// </summary>
    public static PingConfiguration CreateConfiguration(
        string[] hosts,
        string? file,
        int ttl,
        int packetSize,
        int timeout,
        int count,
        int concurrency,
        OutputFormat format,
        string? outputFile,
        int retry,
        int retryDelay,
        bool noProgress,
        bool noStatistics)
    {
        var hostList = new List<string>();

        // Add hosts from command line arguments
        if (hosts.Length > 0)
        {
            hostList.AddRange(hosts);
        }

        return new PingConfiguration
        {
            Hosts = hostList.AsReadOnly(),
            Ttl = ttl,
            PacketSize = packetSize,
            TimeoutMs = timeout,
            PingCount = count,
            MaxConcurrency = concurrency,
            OutputFormat = format,
            OutputFilePath = outputFile,
            RetryAttempts = retry,
            RetryDelayMs = retryDelay,
            ShowProgress = !noProgress,
            IncludeStatistics = !noStatistics
        };
    }
}
