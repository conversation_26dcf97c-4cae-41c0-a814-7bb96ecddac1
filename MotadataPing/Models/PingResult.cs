using System.Net.NetworkInformation;
using System.Text.Json.Serialization;

namespace MotadataPing.Models;

/// <summary>
/// Result record for individual ping operations
/// </summary>
public record PingResult
{
    /// <summary>
    /// Target host that was pinged
    /// </summary>
    public required string Host { get; init; }

    /// <summary>
    /// IP address that was actually pinged (resolved from host)
    /// </summary>
    public string? IpAddress { get; init; }

    /// <summary>
    /// Status of the ping operation
    /// </summary>
    public IPStatus Status { get; init; }

    /// <summary>
    /// Round-trip time in milliseconds (null if ping failed)
    /// </summary>
    public long? RoundTripTimeMs { get; init; }

    /// <summary>
    /// TTL of the reply packet
    /// </summary>
    public int? Ttl { get; init; }

    /// <summary>
    /// Size of the ping packet sent
    /// </summary>
    public int PacketSize { get; init; }

    /// <summary>
    /// Timestamp when the ping was performed
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Error message if ping failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Attempt number (for retry scenarios)
    /// </summary>
    public int AttemptNumber { get; init; } = 1;

    /// <summary>
    /// Whether this ping was successful
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => Status == IPStatus.Success;

    /// <summary>
    /// Human-readable status description
    /// </summary>
    [JsonIgnore]
    public string StatusDescription => Status switch
    {
        IPStatus.Success => "Success",
        IPStatus.TimedOut => "Timed out",
        IPStatus.DestinationHostUnreachable => "Host unreachable",
        IPStatus.DestinationNetworkUnreachable => "Network unreachable",
        IPStatus.DestinationUnreachable => "Destination unreachable",
        IPStatus.BadDestination => "Bad destination",
        IPStatus.BadHeader => "Bad header",
        IPStatus.BadOption => "Bad option",
        IPStatus.BadRoute => "Bad route",
        IPStatus.HardwareError => "Hardware error",
        IPStatus.IcmpError => "ICMP error",
        IPStatus.NoResources => "No resources",
        IPStatus.PacketTooBig => "Packet too big",
        IPStatus.ParameterProblem => "Parameter problem",
        IPStatus.SourceQuench => "Source quench",
        IPStatus.TtlExpired => "TTL expired",
        IPStatus.TtlReassemblyTimeExceeded => "TTL reassembly time exceeded",
        _ => Status.ToString()
    };

    /// <summary>
    /// Creates a successful ping result
    /// </summary>
    public static PingResult Success(string host, string? ipAddress, long roundTripTime, int? ttl, int packetSize, int attemptNumber = 1)
        => new()
        {
            Host = host,
            IpAddress = ipAddress,
            Status = IPStatus.Success,
            RoundTripTimeMs = roundTripTime,
            Ttl = ttl,
            PacketSize = packetSize,
            AttemptNumber = attemptNumber
        };

    /// <summary>
    /// Creates a failed ping result
    /// </summary>
    public static PingResult Failure(string host, string? ipAddress, IPStatus status, int packetSize, string? errorMessage = null, int attemptNumber = 1)
        => new()
        {
            Host = host,
            IpAddress = ipAddress,
            Status = status,
            PacketSize = packetSize,
            ErrorMessage = errorMessage,
            AttemptNumber = attemptNumber
        };
}
