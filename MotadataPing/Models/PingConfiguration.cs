using System.Net.NetworkInformation;

namespace MotadataPing.Models;

/// <summary>
/// Configuration record for ping operations with modern C# features
/// </summary>
public record PingConfiguration
{
    /// <summary>
    /// List of hosts to ping
    /// </summary>
    public required IReadOnlyList<string> Hosts { get; init; }

    /// <summary>
    /// Time To Live (TTL) for ping packets
    /// </summary>
    public int Ttl { get; init; } = 64;

    /// <summary>
    /// Size of ping packets in bytes
    /// </summary>
    public int PacketSize { get; init; } = 32;

    /// <summary>
    /// Timeout for each ping attempt in milliseconds
    /// </summary>
    public int TimeoutMs { get; init; } = 5000;

    /// <summary>
    /// Number of ping attempts per host
    /// </summary>
    public int PingCount { get; init; } = 4;

    /// <summary>
    /// Maximum number of concurrent ping operations
    /// </summary>
    public int MaxConcurrency { get; init; } = 10;

    /// <summary>
    /// Output format for results
    /// </summary>
    public OutputFormat OutputFormat { get; init; } = OutputFormat.Console;

    /// <summary>
    /// Output file path (optional)
    /// </summary>
    public string? OutputFilePath { get; init; }

    /// <summary>
    /// Whether to show progress during bulk operations
    /// </summary>
    public bool ShowProgress { get; init; } = true;

    /// <summary>
    /// Whether to include detailed statistics
    /// </summary>
    public bool IncludeStatistics { get; init; } = true;

    /// <summary>
    /// Retry attempts for failed pings
    /// </summary>
    public int RetryAttempts { get; init; } = 1;

    /// <summary>
    /// Delay between retry attempts in milliseconds
    /// </summary>
    public int RetryDelayMs { get; init; } = 1000;

    /// <summary>
    /// Validates the configuration and returns any validation errors
    /// </summary>
    public IEnumerable<string> Validate()
    {
        if (!Hosts.Any())
            yield return "At least one host must be specified";

        if (Ttl is < 1 or > 255)
            yield return "TTL must be between 1 and 255";

        if (PacketSize is < 1 or > 65500)
            yield return "Packet size must be between 1 and 65500 bytes";

        if (TimeoutMs < 1)
            yield return "Timeout must be greater than 0";

        if (PingCount < 1)
            yield return "Ping count must be greater than 0";

        if (MaxConcurrency < 1)
            yield return "Max concurrency must be greater than 0";

        if (RetryAttempts < 0)
            yield return "Retry attempts cannot be negative";

        if (RetryDelayMs < 0)
            yield return "Retry delay cannot be negative";

        foreach (var host in Hosts)
        {
            if (string.IsNullOrWhiteSpace(host))
                yield return "Host cannot be empty or whitespace";
        }
    }
}

/// <summary>
/// Output format options
/// </summary>
public enum OutputFormat
{
    Console,
    Csv,
    Json
}
