using System.Net.NetworkInformation;

namespace MotadataPing.Models;

/// <summary>
/// Configuration record for ping operations with fping-compatible defaults
/// </summary>
public record PingConfiguration
{
    /// <summary>
    /// List of hosts to ping
    /// </summary>
    public required IReadOnlyList<string> Hosts { get; init; }

    /// <summary>
    /// Time To Live (TTL) for ping packets (fping default: system default, usually 64)
    /// </summary>
    public int Ttl { get; init; } = 64;

    /// <summary>
    /// Size of ping packets in bytes (fping default: 56 bytes, same as ping)
    /// </summary>
    public int PacketSize { get; init; } = 56;

    /// <summary>
    /// Timeout for each ping attempt in milliseconds (fping default: 500ms)
    /// </summary>
    public int TimeoutMs { get; init; } = 500;

    /// <summary>
    /// Number of ping attempts per host (fping default: 3 when using count mode)
    /// </summary>
    public int PingCount { get; init; } = 3;

    /// <summary>
    /// Maximum number of concurrent ping operations (optimized for fping-like performance)
    /// fping sends packets with 10ms minimum interval, so higher concurrency is beneficial
    /// </summary>
    public int MaxConcurrency { get; init; } = 25;

    /// <summary>
    /// Output format for results
    /// </summary>
    public OutputFormat OutputFormat { get; init; } = OutputFormat.Console;

    /// <summary>
    /// Output file path (optional)
    /// </summary>
    public string? OutputFilePath { get; init; }

    /// <summary>
    /// Whether to show progress during bulk operations
    /// </summary>
    public bool ShowProgress { get; init; } = true;

    /// <summary>
    /// Whether to include detailed statistics
    /// </summary>
    public bool IncludeStatistics { get; init; } = true;

    /// <summary>
    /// Retry attempts for failed pings (fping default: 3 retries)
    /// </summary>
    public int RetryAttempts { get; init; } = 3;

    /// <summary>
    /// Delay between retry attempts in milliseconds (fping uses exponential backoff starting from timeout)
    /// fping default backoff factor is 1.5, so: 500ms, 750ms, 1125ms
    /// </summary>
    public int RetryDelayMs { get; init; } = 500;

    /// <summary>
    /// Minimum interval between sending packets to any target in milliseconds (fping default: 10ms)
    /// This controls the overall pacing of the ping operations
    /// </summary>
    public int IntervalMs { get; init; } = 10;

    /// <summary>
    /// Period between successive packets to the same target in milliseconds (fping default: 1000ms)
    /// This is the time to wait before sending another ping to the same host
    /// </summary>
    public int PeriodMs { get; init; } = 1000;

    /// <summary>
    /// Validates the configuration and returns any validation errors
    /// </summary>
    public IEnumerable<string> Validate()
    {
        if (!Hosts.Any())
            yield return "At least one host must be specified";

        if (Ttl is < 1 or > 255)
            yield return "TTL must be between 1 and 255";

        if (PacketSize is < 12 or > 65500)
            yield return "Packet size must be between 12 and 65500 bytes (fping minimum is 12)";

        if (TimeoutMs < 1)
            yield return "Timeout must be greater than 0";

        if (PingCount < 1)
            yield return "Ping count must be greater than 0";

        if (MaxConcurrency < 1)
            yield return "Max concurrency must be greater than 0";

        if (RetryAttempts < 0)
            yield return "Retry attempts cannot be negative";

        if (RetryDelayMs < 0)
            yield return "Retry delay cannot be negative";

        if (IntervalMs < 1)
            yield return "Interval must be at least 1ms (fping minimum)";

        if (PeriodMs < 10)
            yield return "Period must be at least 10ms (fping minimum)";

        foreach (var host in Hosts)
        {
            if (string.IsNullOrWhiteSpace(host))
                yield return "Host cannot be empty or whitespace";
        }
    }
}

/// <summary>
/// Output format options
/// </summary>
public enum OutputFormat
{
    Console,
    Csv,
    Json
}
