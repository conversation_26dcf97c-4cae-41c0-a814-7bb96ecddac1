using System.Text.Json.Serialization;

namespace MotadataPing.Models;

/// <summary>
/// Statistics record for ping operations summary
/// </summary>
public record PingStatistics
{
    /// <summary>
    /// Total number of hosts pinged
    /// </summary>
    public int TotalHosts { get; init; }

    /// <summary>
    /// Total number of ping attempts made
    /// </summary>
    public int TotalAttempts { get; init; }

    /// <summary>
    /// Number of successful ping attempts
    /// </summary>
    public int SuccessfulAttempts { get; init; }

    /// <summary>
    /// Number of failed ping attempts
    /// </summary>
    public int FailedAttempts { get; init; }

    /// <summary>
    /// Success rate as a percentage
    /// </summary>
    [JsonIgnore]
    public double SuccessRate => TotalAttempts > 0 ? (double)SuccessfulAttempts / TotalAttempts * 100 : 0;

    /// <summary>
    /// Packet loss rate as a percentage
    /// </summary>
    [JsonIgnore]
    public double PacketLossRate => TotalAttempts > 0 ? (double)FailedAttempts / TotalAttempts * 100 : 0;

    /// <summary>
    /// Minimum round-trip time in milliseconds
    /// </summary>
    public long? MinRoundTripTimeMs { get; init; }

    /// <summary>
    /// Maximum round-trip time in milliseconds
    /// </summary>
    public long? MaxRoundTripTimeMs { get; init; }

    /// <summary>
    /// Average round-trip time in milliseconds
    /// </summary>
    public double? AverageRoundTripTimeMs { get; init; }

    /// <summary>
    /// Total execution time for all ping operations
    /// </summary>
    public TimeSpan TotalExecutionTime { get; init; }

    /// <summary>
    /// Number of hosts that were completely unreachable
    /// </summary>
    public int UnreachableHosts { get; init; }

    /// <summary>
    /// Number of hosts that had at least one successful ping
    /// </summary>
    public int ReachableHosts { get; init; }

    /// <summary>
    /// Creates statistics from a collection of ping results
    /// </summary>
    public static PingStatistics FromResults(IEnumerable<PingResult> results, TimeSpan executionTime)
    {
        var resultsList = results.ToList();
        var successfulResults = resultsList.Where(r => r.IsSuccess).ToList();
        var hostGroups = resultsList.GroupBy(r => r.Host).ToList();

        var roundTripTimes = successfulResults
            .Where(r => r.RoundTripTimeMs.HasValue)
            .Select(r => r.RoundTripTimeMs!.Value)
            .ToList();

        return new PingStatistics
        {
            TotalHosts = hostGroups.Count,
            TotalAttempts = resultsList.Count,
            SuccessfulAttempts = successfulResults.Count,
            FailedAttempts = resultsList.Count - successfulResults.Count,
            MinRoundTripTimeMs = roundTripTimes.Any() ? roundTripTimes.Min() : null,
            MaxRoundTripTimeMs = roundTripTimes.Any() ? roundTripTimes.Max() : null,
            AverageRoundTripTimeMs = roundTripTimes.Any() ? roundTripTimes.Average() : null,
            TotalExecutionTime = executionTime,
            UnreachableHosts = hostGroups.Count(g => g.All(r => !r.IsSuccess)),
            ReachableHosts = hostGroups.Count(g => g.Any(r => r.IsSuccess))
        };
    }

    /// <summary>
    /// Returns a formatted summary string
    /// </summary>
    public string GetSummary()
    {
        var summary = new List<string>
        {
            $"Total Hosts: {TotalHosts}",
            $"Total Attempts: {TotalAttempts}",
            $"Successful: {SuccessfulAttempts} ({SuccessRate:F1}%)",
            $"Failed: {FailedAttempts} ({PacketLossRate:F1}%)",
            $"Reachable Hosts: {ReachableHosts}",
            $"Unreachable Hosts: {UnreachableHosts}",
            $"Execution Time: {TotalExecutionTime.TotalSeconds:F2}s"
        };

        if (AverageRoundTripTimeMs.HasValue)
        {
            summary.Add($"Round-trip time: min={MinRoundTripTimeMs}ms, max={MaxRoundTripTimeMs}ms, avg={AverageRoundTripTimeMs:F1}ms");
        }

        return string.Join(Environment.NewLine, summary);
    }
}
