# Motadata Ping Utility

A production-ready bulk ping utility for Windows OS built with .NET 8.0 and modern C# features.

## Features

- **Bulk ping operations** with configurable concurrency
- **Multiple input formats**: Command line, CSV files, TXT files
- **Multiple output formats**: Console, CSV, JSON
- **Comprehensive error handling** and logging
- **Progress reporting** for bulk operations
- **Statistics reporting** with success rates and timing analysis
- **Retry logic** for failed pings
- **IPv4 and IPv6 support**
- **Modern C# features**: records, pattern matching, nullable reference types

## Installation

1. Ensure you have .NET 8.0 or later installed
2. Clone or download the source code
3. Build the project:
   ```bash
   dotnet build --configuration Release
   ```

## Usage

### Basic Usage

```bash
# Ping single host
MotadataPing.exe google.com

# Ping multiple hosts
MotadataPing.exe google.com microsoft.com github.com

# Ping hosts from file
MotadataPing.exe --file hosts.txt

# Ping with custom settings
MotadataPing.exe google.com --count 10 --timeout 3000 --ttl 128
```

### Advanced Usage

```bash
# Bulk ping with high concurrency
MotadataPing.exe --file hosts.txt --concurrency 20 --count 5

# Save results to CSV
MotadataPing.exe --file hosts.txt --format Csv --output-file results.csv

# Save results to JSON with retry logic
MotadataPing.exe --file hosts.txt --format Json --output-file results.json --retry 3 --retry-delay 2000

# Quiet mode (no progress, no statistics)
MotadataPing.exe --file hosts.txt --no-progress --no-statistics

# Verbose logging
MotadataPing.exe google.com --verbose
```

## Command Line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--file` | `-f` | Path to file containing hosts | - |
| `--ttl` | `-t` | Time To Live for ping packets | 64 |
| `--size` | `-s` | Packet size in bytes | 32 |
| `--timeout` | `-w` | Timeout in milliseconds | 5000 |
| `--count` | `-c` | Number of pings per host | 4 |
| `--concurrency` | `-j` | Max concurrent operations | 10 |
| `--format` | `-o` | Output format (Console/Csv/Json) | Console |
| `--output-file` | `-O` | Output file path | - |
| `--retry` | `-r` | Retry attempts for failed pings | 1 |
| `--retry-delay` | `-d` | Delay between retries (ms) | 1000 |
| `--no-progress` | `-q` | Disable progress reporting | false |
| `--no-statistics` | `-n` | Disable statistics | false |
| `--verbose` | `-v` | Enable verbose logging | false |

## Input File Formats

### TXT Format
```
google.com
microsoft.com
github.com
***********
2001:db8::1
```

### CSV Format
```
google.com,microsoft.com,github.com
***********
2001:db8::1,example.com
```

### Comments and Empty Lines
```
# This is a comment
google.com
microsoft.com

# Another comment
github.com
```

## Output Formats

### Console Output
Real-time display of ping results with colored output:
```
[14:30:15] PING google.com (**************): time=23ms TTL=118
[14:30:16] PING microsoft.com (**************): time=45ms TTL=115
[14:30:17] PING invalid-host.example: Host unreachable
```

### CSV Output
```csv
Host,IpAddress,Status,RoundTripTimeMs,Ttl,PacketSize,Timestamp,ErrorMessage,AttemptNumber
google.com,**************,Success,23,118,32,2024-01-15 14:30:15.123,,1
microsoft.com,**************,Success,45,115,32,2024-01-15 14:30:16.456,,1
```

### JSON Output
```json
{
  "generatedAt": "2024-01-15T14:30:20.000Z",
  "results": [
    {
      "host": "google.com",
      "ipAddress": "**************",
      "status": "Success",
      "roundTripTimeMs": 23,
      "ttl": 118,
      "packetSize": 32,
      "timestamp": "2024-01-15T14:30:15.123Z",
      "attemptNumber": 1
    }
  ]
}
```

## Statistics Report

```
=== PING STATISTICS ===
Total Hosts: 3
Total Attempts: 12
Successful: 10 (83.3%)
Failed: 2 (16.7%)
Reachable Hosts: 2
Unreachable Hosts: 1
Execution Time: 5.23s
Round-trip time: min=23ms, max=156ms, avg=67.4ms
```

## Error Handling

The utility provides comprehensive error handling for:
- DNS resolution failures
- Network timeouts
- Invalid host formats
- File access errors
- Network interruptions
- Invalid command line arguments

## Performance Considerations

- **Concurrency**: Default is 10 concurrent operations. Increase for better performance but be mindful of network flooding
- **Timeouts**: Default 5-second timeout balances responsiveness with reliability
- **Retry Logic**: Helps handle temporary network issues
- **Memory Management**: Proper disposal of resources and async patterns

## Examples

### Example 1: Basic Network Monitoring
```bash
MotadataPing.exe --file network-devices.txt --count 1 --format Csv --output-file network-status.csv
```

### Example 2: Detailed Host Analysis
```bash
MotadataPing.exe critical-servers.txt --count 10 --retry 2 --verbose --format Json --output-file detailed-analysis.json
```

### Example 3: Quick Connectivity Check
```bash
MotadataPing.exe ******* ******* --count 1 --no-statistics
```

## Requirements

- Windows OS
- .NET 8.0 or later
- Administrator privileges may be required for ICMP operations

## License

Copyright © 2024. All rights reserved.
